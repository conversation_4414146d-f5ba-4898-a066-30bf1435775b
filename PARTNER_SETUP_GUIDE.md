# Guide de Configuration des Partenaires

## Problème Résolu

Le système de filtrage pour les utilisateurs avec le rôle "Partenaire" a été corrigé. Auparavant, le système cherchait un partenaire créé par l'utilisateur (`utilisateurCreationId`), mais la logique correcte est de lier l'utilisateur à un partenaire existant.

## Solution Implémentée

### Mécanisme de Liaison

Les utilisateurs partenaires sont maintenant liés aux enregistrements partenaires via **l'email de contact** :

```typescript
// Recherche du partenaire associé par email
const partenaire = await prisma.partenaire.findFirst({
  where: {
    contact: {
      path: ['email'],
      equals: utilisateur.email
    }
  }
});
```

### APIs Modifiées

1. **`/api/allocations`** - Filtrage par partenaire
2. **`/api/garanties`** - Filtrage par partenaire  
3. **`/api/mainlevees`** - Filtrage par partenaire
4. **`/api/mises-en-jeu`** - Filtrage par partenaire
5. **`/api/garanties/[id]`** - Filtrage par partenaire

## Configuration d'un Utilisateur Partenaire

### Étape 1: Créer l'Utilisateur

```sql
INSERT INTO "Utilisateur" (
  "nomUtilisateur", "email", "motDePasse", "nom", "prenom", "role", "estActif"
) VALUES (
  'partenaire_test', '<EMAIL>', '[mot_de_passe_haché]', 'Test', 'Partenaire', 'Partenaire', true
);
```

### Étape 2: Créer l'Enregistrement Partenaire

```sql
INSERT INTO "Partenaire" (
  "nom", "typePartenaire", "contact", "utilisateurCreationId"
) VALUES (
  'Banque Test', 'Banque', 
  '{"email": "<EMAIL>", "nomRepresentant": "Test Partenaire"}',
  1  -- ID de l'admin qui crée le partenaire
);
```

### Étape 3: Script Automatisé

Utilisez le script fourni :

```bash
# Compiler et exécuter le script
node scripts/run-create-partner.js
```

## Test de la Solution

### Utilisateur de Test Créé

- **Email**: `<EMAIL>`
- **Mot de passe**: `Partner123!`
- **Rôle**: `Partenaire`

### Vérification

1. Connectez-vous avec les identifiants partenaire
2. Vérifiez que le sidebar affiche la section "Mes Garanties"
3. Testez l'accès aux APIs :
   - `/api/allocations` ✅
   - `/api/garanties` ✅
   - `/api/mainlevees` ✅
   - `/api/mises-en-jeu` ✅

### Données Filtrées

Le partenaire ne verra que :
- Ses propres allocations
- Ses propres garanties
- Les mainlevées de ses garanties
- Les mises en jeu de ses garanties

## Structure de Données

### Table Utilisateur
```
id | email              | role       | nom  | prenom
3  | <EMAIL>| Partenaire | Test | Partenaire
```

### Table Partenaire
```
id | nom               | contact.email       | utilisateurCreationId
1  | Banque Test       | <EMAIL> | 1
```

### Liaison
```
Utilisateur.email === Partenaire.contact.email
```

## Sécurité

- ✅ Filtrage automatique par email
- ✅ Vérification de l'existence du lien
- ✅ Erreur 403 si aucun partenaire associé
- ✅ Logs de sécurité pour traçabilité

## Maintenance

Pour ajouter un nouveau partenaire :

1. Créer l'utilisateur avec le rôle "Partenaire"
2. Créer l'enregistrement partenaire avec le même email dans `contact.email`
3. La liaison sera automatique via l'email
