-- Migration pour ajouter la liaison utilisateur-partenaire
-- Ajouter le champ utilisateurPartenaireId à la table Partenaire

ALTER TABLE "Partenaire" ADD COLUMN "utilisateurPartenaireId" INTEGER;

-- Ajouter la contrainte de clé étrangère
ALTER TABLE "Partenaire" ADD CONSTRAINT "Partenaire_utilisateurPartenaireId_fkey" 
FOREIGN KEY ("utilisateurPartenaireId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Ajouter un index pour améliorer les performances
CREATE INDEX "Partenaire_utilisateurPartenaireId_idx" ON "Partenaire"("utilisateurPartenaireId");

-- Ajouter une contrainte d'unicité pour s'assurer qu'un utilisateur ne peut être lié qu'à un seul partenaire
ALTER TABLE "Partenaire" ADD CONSTRAINT "Partenaire_utilisateurPartenaireId_unique" UNIQUE ("utilisateurPartenaireId");
