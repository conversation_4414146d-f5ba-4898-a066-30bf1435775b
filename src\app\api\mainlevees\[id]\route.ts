import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur, StatutMainlevee, StatutGarantie } from "@prisma/client";
import { DecisionMainleveeSchema } from "@/lib/schemas/mainlevee.schema";
import { auditContext } from '@/lib/prisma-audit.middleware';
import { headers } from 'next/headers';
import { Decimal } from "@prisma/client/runtime/library";

interface RouteParams { params: { id: string } }

// GET pour récupérer une mainlevée (si besoin pour un formulaire de détail/modif de décision)
export async function GET(request: Request, context: RouteParams) {
    const { params } = context;
    const session = await getServerSession(authOptions);
    if (
      !session ||
      !([RoleUtilisateur.Administrateur, RoleUtilisateur.GestionnaireGesGar] as RoleUtilisateur[]).includes(session.user?.role as RoleUtilisateur)
    ) {
        return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
    }

    const resolvedParams = await params;
    const mainleveeId = parseInt(resolvedParams.id);
    if (isNaN(mainleveeId)) return NextResponse.json({ message: "ID de mainlevée invalide" }, { status: 400 });

    try {
        const mainlevee = await prisma.mainlevee.findUnique({
            where: { id: mainleveeId },
            include: { garantie: { select: { referenceGarantie: true } } }
        });
        if (!mainlevee) return NextResponse.json({ message: "Demande de mainlevée non trouvée" }, { status: 404 });
        return NextResponse.json(mainlevee);
    } catch (error) {
        console.error(`Erreur GET /api/mainlevees/${mainleveeId}:`, error);
        return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
}

// PUT: Traiter (approuver/refuser) une demande de mainlevée
export async function PUT(request: Request, context: RouteParams) {
  const { params } = context;
  const session = await getServerSession(authOptions);
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role)) {
    return NextResponse.json({ message: "Non autorisé à traiter les mainlevées" }, { status: 403 });
  }

  const resolvedParams = await params;
  const mainleveeId = parseInt(resolvedParams.id);
  if (isNaN(mainleveeId)) {
    return NextResponse.json({ message: "ID de mainlevée invalide" }, { status: 400 });
  }

  const processorId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  return auditContext.run({ userId: processorId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const body = await request.json();
      if (body.dateDecision) body.dateDecision = new Date(body.dateDecision);

      const validation = DecisionMainleveeSchema.safeParse(body);
      if (!validation.success) {
        return NextResponse.json({ message: "Données de décision invalides", errors: validation.error.formErrors.fieldErrors }, { status: 400 });
      }

      const { statut: nouveauStatutMainlevee, dateDecision, montantRecupereStr, commentairesDecision, raisonRefus } = validation.data;
      let montantRecupereDecimal: Decimal | null = null;
if (montantRecupereStr && montantRecupereStr.trim() !== "") {
  const normalised = montantRecupereStr.replace(",", ".");
  if (!/^\d+(\.\d+)?$/.test(normalised)) {
    return NextResponse.json({ message: "montantRecupere invalide" }, { status: 400 });
  }
  montantRecupereDecimal = new Decimal(normalised);
}

      // --- Début Transaction ---
      const result = await prisma.$transaction(async (tx) => {
        // 1. Récupérer la mainlevée et sa garantie associée (avec l'allocation)
        const mainlevee = await tx.mainlevee.findUnique({
          where: { id: mainleveeId },
          include: {
            garantie: {
              include: {
                allocation: true // Nécessaire pour mettre à jour le montant disponible de l'allocation
              }
            }
          }
        });

        if (!mainlevee) throw new Error("Demande de mainlevée non trouvée.");
        if (!mainlevee.garantie) throw new Error("Garantie associée à la mainlevée non trouvée.");
        // Sécurité : allocation peut être null si la relation n'existe pas
        const allocation = mainlevee.garantie.allocation;
        if (!allocation) throw new Error("Allocation associée à la garantie non trouvée.");

        // 2. On ne peut traiter que les demandes 'Demandee' ou 'EnCoursApprobation'
        if (mainlevee.statut !== StatutMainlevee.Demandee && mainlevee.statut !== StatutMainlevee.EnCoursApprobation) {
          throw new Error(`Cette demande de mainlevée a déjà été traitée (Statut actuel: ${mainlevee.statut}).`);
        }

        // 3. Mettre à jour la Mainlevee
        const updatedMainlevee = await tx.mainlevee.update({
          where: { id: mainleveeId },
          data: {
            statut: nouveauStatutMainlevee, // Sera 'Accordee' ou 'Refusee'
            dateDecision,
            montantRecupere: montantRecupereDecimal,
            commentairesDecision,
            raisonRefus: nouveauStatutMainlevee === StatutMainlevee.Refusee ? raisonRefus : null,
            utilisateurModificationId: processorId,
          },
        });

        // 4. Mettre à jour la Garantie et l'Allocation si la mainlevée est Accordée
        let updatedGarantieStatut = mainlevee.garantie.statut;
        let montantARestituer = new Decimal(0);

        if (nouveauStatutMainlevee === StatutMainlevee.Accordee) {
          updatedGarantieStatut = StatutGarantie.MainleveeAccordee;

          // Restituer le montant garanti au disponible de l'allocation
          // Sauf si un montant a été récupéré (cas d'une mise en jeu partielle suivie d'une mainlevée)
          // Dans ce cas, on restitue montantGarantie - montantRecupere (si pertinent)
          // Pour une mainlevée standard (remboursement total), montantRecupere est null.
          montantARestituer = montantRecupereDecimal !== null
            ? mainlevee.garantie.montantGarantie.sub(montantRecupereDecimal)
            : mainlevee.garantie.montantGarantie;

          // Ajoutez un garde-fou
          if (montantARestituer.isNegative()) {
            throw new Error("Le montant récupéré dépasse le montant garanti.");
          }

          // Mise à jour de l'allocation UNIQUEMENT si Accordée
          const nouveauMontantDisponibleAlloc = allocation.montantDisponible.add(montantARestituer);
          await tx.allocationLignePartenaire.update({
            where: { id: allocation.id },
            data: {
              montantDisponible: nouveauMontantDisponibleAlloc,
              utilisateurModificationId: processorId,
            },
          });
        } else if (nouveauStatutMainlevee === StatutMainlevee.Refusee) {
          // Si la mainlevée est refusée, la garantie revient à son statut précédent
          // ou à un statut indiquant le refus.
          // Pour simplifier, si elle était 'MainleveeDemandee', on la remet 'Active' (ou son statut d'origine avant demande).
          // Une meilleure approche serait de stocker le statut précédent la demande.
          if (mainlevee.garantie.statut === StatutGarantie.MainleveeDemandee) {
              // TODO: Retrouver le statut précédent la demande. Pour l'instant, on met Active.
              // Cela pourrait être incorrect si la garantie était EnSouffrance ou Echue avant la demande.
              updatedGarantieStatut = StatutGarantie.Active;
          } else {
              updatedGarantieStatut = StatutGarantie.MainleveeRefusee; // Ou garder le statut actuel de la garantie
          }
          // Ne pas mettre à jour l'allocation si refusée
        }

        // Mettre à jour la garantie seulement si son statut a changé
        if (mainlevee.garantie.statut !== updatedGarantieStatut) {
            await tx.garantie.update({
                where: { id: mainlevee.garantieId },
                data: {
                    statut: updatedGarantieStatut,
                    utilisateurModificationId: processorId,
                },
            });
        }
        return updatedMainlevee;
      });
      // --- Fin Transaction ---
      return NextResponse.json(result);

    } catch (error: any) {
      console.error(`Erreur PUT /api/mainlevees/${mainleveeId}:`, error);
      if (error.message.includes("non trouvée") || error.message.includes("déjà été traitée")) {
        return NextResponse.json({ message: error.message }, { status: 400 });
      }
      if (error.code === 'P2025') return NextResponse.json({ message: "Entité non trouvée pour la mise à jour" }, { status: 404 });
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}