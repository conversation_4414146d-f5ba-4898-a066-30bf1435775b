// src/app/api/garanties/[id]/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { Prisma } from "@prisma/client";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route"; // Ajustez le chemin si nécessaire
import { RoleUtilisateur, StatutGarantie } from "@prisma/client";
import { headers } from "next/headers";
import { BusinessAction, withAudit } from '@/lib/audit-wrapper';
import { auditContext } from '@/lib/auditContext';
// Pas besoin d'importer le type AuditContext explicitement si on utilise les types inférés
// ou si le type est déjà globalement disponible via tsconfig.
// Si une erreur persiste, on peut définir un type localement ou vérifier l'export de AuditContext.
// Pour l'instant, on le retire pour voir si l'inférence suffit.
import { UpdateGarantieSchema } from "@/lib/schemas/garantie.schema";
import { Decimal } from "@prisma/client/runtime/library";

type RouteParams = { params: { id: string } };

export async function GET(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  // Ajuster les rôles autorisés selon qui peut voir les détails d'une garantie
  if (
    !session ||
    !["Administrateur", "GestionnaireGesGar", "AnalysteFinancier", "Partenaire"].includes(session.user?.role as string)
  ) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const garantieId = parseInt(resolvedParams.id);
  if (isNaN(garantieId)) {
    return NextResponse.json({ message: "ID de garantie invalide" }, { status: 400 });
  }

  // Pour le rôle Partenaire, s'assurer qu'il ne voit que ses propres garanties
  const whereClauseForPartenaire: any = {};
  if (session.user?.role === RoleUtilisateur.Partenaire) {
    // Trouver le partenaire associé à cet utilisateur via la nouvelle liaison
    const utilisateur = await prisma.utilisateur.findUnique({
      where: { id: parseInt(session.user.id) },
      select: {
        partenaireAssocie: {
          select: { id: true, nom: true }
        }
      }
    });

    if (!utilisateur?.partenaireAssocie) {
      console.error(`[SECURITE] Utilisateur Partenaire sans lien Partenaire: id=${session.user.id}`);
      return NextResponse.json({ message: "Aucun partenaire associé à cet utilisateur" }, { status: 403 });
    }

    whereClauseForPartenaire.partenaireId = utilisateur.partenaireAssocie.id;
  }


  try {
    const garantie = await prisma.garantie.findUnique({
      where: {
        id: garantieId,
        ...whereClauseForPartenaire
      },
      include: {
        ligneGarantie: {
          select: { id: true, nom: true, devise: true, bailleur: { select: { nom: true } } }
        },
        allocation: {
          select: {
            id: true,
            referenceConvention: true,
            partenaire: { select: { id: true, nom: true, typePartenaire: true } },
          }
        },
        projet: {
          select: {
            id: true,
            nom: true,
            description: true,
            secteurActivite: { select: { id: true, nom: true } },
            clientBeneficiaire: {
              select: { id: true, nomOuRaisonSociale: true, typeClient: true, identifiantUnique: true }
            }
          }
        },
        utilisateurCreation: { select: { nomUtilisateur: true, nom: true, prenom: true } },
        utilisateurModification: { select: { nomUtilisateur: true, nom: true, prenom: true } },
      },
    });

    if (!garantie) {
      return NextResponse.json({ message: "Garantie non trouvée" }, { status: 404 });
    }
    return NextResponse.json(garantie);
  } catch (error) {
    console.error(`Erreur lors de la récupération de la garantie ${garantieId}:`, error);
    return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
  }
}

// src/app/api/garanties/[id]/route.ts
// ... (imports existants, y compris Decimal, GarantieSchema, UpdateGarantieSchema, auditContext, headers) ...

// ... (fonction GET existante) ...

export async function PUT(request: Request, { params }: { params: { id: string } }) {
  // params is a plain object, no need to await
  const resolvedParams = params;
  const garantieIdString = resolvedParams.id;
  const garantieId = parseInt(garantieIdString);

  if (isNaN(garantieId)) {
    return NextResponse.json({ message: "ID de garantie invalide fourni dans l'URL." }, { status: 400 });
  }

  const session = await getServerSession(authOptions);
  if (
    !session ||
    !session.user ||
    (session.user.role !== RoleUtilisateur.Administrateur && session.user.role !== RoleUtilisateur.GestionnaireGesGar)
  ) {
    return NextResponse.json({ message: "Non autorisé à modifier cette garantie" }, { status: 403 });
  }

  const modifierId = session.user?.id ? parseInt(session.user.id) : undefined;

  // 2. Appel SYNCHRONE à headers() car elle retourne un objet Headers
  const headersList =await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  const operation = `GARANTIE_UPDATE - ID: ${garantieId}`;

  return auditContext.run({ userId: modifierId, ipAddress: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined, operation: operation }, async () => {
    try {
      const body = await request.json();

      if (body.dateEcheanceGarantie) body.dateEcheanceGarantie = new Date(body.dateEcheanceGarantie);
      if (body.dateAccordGarantie) body.dateAccordGarantie = new Date(body.dateAccordGarantie);
      if (body.dateEffetGarantie) body.dateEffetGarantie = new Date(body.dateEffetGarantie);
      if (body.dateDernierRemboursementClient) body.dateDernierRemboursementClient = new Date(body.dateDernierRemboursementClient);

      const validation = UpdateGarantieSchema.safeParse(body);
      if (!validation.success) {
        return NextResponse.json({ message: "Données de mise à jour invalides", errors: validation.error.formErrors.fieldErrors }, { status: 400 });
      }

      const validatedData = validation.data;
      const dataToUpdate: Prisma.GarantieUncheckedUpdateInput = {};

      // Define the allowed keys explicitly for type safety
      const champsModifiablesDirectement: Array<keyof Prisma.GarantieUncheckedUpdateInput & keyof typeof validatedData> = [
        "identifiantCreditPartenaire", "conditionsParticulieres"
      ];
      champsModifiablesDirectement.forEach(key => {
        if (validatedData[key] !== undefined) {
          if (validatedData[key] !== null) {
            dataToUpdate[key] = validatedData[key];
          }
        }
      });

      if (
        validatedData.delaiMiseEnJeu !== undefined &&
        validatedData.delaiMiseEnJeu !== null &&
        (typeof validatedData.delaiMiseEnJeu !== "string" || validatedData.delaiMiseEnJeu.trim() !== "")
      ) {
        dataToUpdate.delaiMiseEnJeu =
          typeof validatedData.delaiMiseEnJeu === "string"
            ? parseInt(validatedData.delaiMiseEnJeu, 10)
            : Number(validatedData.delaiMiseEnJeu);
      } else if (
        validatedData.delaiMiseEnJeu === null ||
        (typeof validatedData.delaiMiseEnJeu === "string" && validatedData.delaiMiseEnJeu.trim() === "")
      ) {
        // Permettre de vider le champ s'il est optionnel dans la DB
        // dataToUpdate.delaiMiseEnJeu = null; // Si le champ DB peut être null
      }


      if (validatedData.dateEcheanceGarantie) dataToUpdate.dateEcheanceGarantie = validatedData.dateEcheanceGarantie;
      if (validatedData.dateAccordGarantie) dataToUpdate.dateAccordGarantie = validatedData.dateAccordGarantie;
      if (validatedData.dateEffetGarantie) dataToUpdate.dateEffetGarantie = validatedData.dateEffetGarantie;

      if (validatedData.dateDernierRemboursementClient) dataToUpdate.dateDernierRemboursementClient = validatedData.dateDernierRemboursementClient;
      if (validatedData.montantRestantDuCreditStr !== undefined) {
        dataToUpdate.montantRestantDuCredit = (validatedData.montantRestantDuCreditStr.trim() === "")
            ? null
            : new Decimal(validatedData.montantRestantDuCreditStr.replace(',', '.'));
      }
      if (validatedData.nombreEcheancesImpayeesStr !== undefined) {
        dataToUpdate.nombreEcheancesImpayees =
          typeof validatedData.nombreEcheancesImpayeesStr === "string"
            ? (validatedData.nombreEcheancesImpayeesStr.trim() === ""
                ? null
                : parseInt(validatedData.nombreEcheancesImpayeesStr, 10))
            : validatedData.nombreEcheancesImpayeesStr;
      }

      const garantieActuelle = await prisma.garantie.findUnique({
        where: { id: garantieId },
        include: { allocation: true }
      });

      if (!garantieActuelle) {
        return NextResponse.json({ message: "Garantie non trouvée pour la mise à jour." }, { status: 404 });
      }
      if (!garantieActuelle.allocation) {
        return NextResponse.json({ message: "Allocation parente non trouvée pour cette garantie." }, { status: 404 });
      }

      const ancienStatut = garantieActuelle.statut;
      const nouveauStatut = validatedData.statut as StatutGarantie | undefined;

      if (nouveauStatut && ancienStatut !== nouveauStatut) {
        dataToUpdate.statut = nouveauStatut;

        const statutsConsommateurs: StatutGarantie[] = [
          StatutGarantie.Validee, StatutGarantie.Active, StatutGarantie.EnSouffrance,
          StatutGarantie.MiseEnJeuDemandee, StatutGarantie.MiseEnJeuAcceptee,
        ];
        const statutsLiberateurs: StatutGarantie[] = [
          StatutGarantie.Echue, StatutGarantie.MainleveeAccordee, StatutGarantie.Radiee,
          StatutGarantie.ClotureeAnormalement, StatutGarantie.MiseEnJeuRefusee, StatutGarantie.Supprimee
        ];

        const ancienStatutConsommait = statutsConsommateurs.includes(ancienStatut);
        const nouveauStatutConsomme = statutsConsommateurs.includes(nouveauStatut);

        let montantImpactAllocation = new Decimal(0);

        if (ancienStatutConsommait && statutsLiberateurs.includes(nouveauStatut)) {
          montantImpactAllocation = garantieActuelle.montantGarantie;
        } else if (!ancienStatutConsommait && nouveauStatutConsomme) {
          if (garantieActuelle.montantGarantie.greaterThan(garantieActuelle.allocation.montantDisponible)) {
            return NextResponse.json({ message: `Activation/Validation impossible: Montant de garantie (${garantieActuelle.montantGarantie.toFixed(2)}) dépasse le disponible (${garantieActuelle.allocation.montantDisponible.toFixed(2)}) de l'allocation.` }, { status: 400 });
          }
          montantImpactAllocation = garantieActuelle.montantGarantie.negated();
        }

        const updatedGarantie = await prisma.$transaction(async (tx) => {
          if (!montantImpactAllocation.isZero()) {
            await tx.allocationLignePartenaire.update({
              where: { id: garantieActuelle.allocationId },
              data: {
                montantDisponible: { increment: montantImpactAllocation },
                utilisateurModificationId: modifierId,
              },
            });
          }
          return tx.garantie.update({
            where: { id: garantieId },
            data: { ...dataToUpdate, utilisateurModificationId: modifierId === undefined ? null : modifierId },
          });
        });
        return NextResponse.json(updatedGarantie);

      } else {
        if (validatedData.statut && validatedData.statut === ancienStatut) {
            dataToUpdate.statut = validatedData.statut as StatutGarantie; // Assurer que le statut est dans dataToUpdate s'il a été envoyé même si identique
        } else if (validatedData.statut && validatedData.statut !== ancienStatut) {
            // Ce cas est couvert par le if précédent. Théoriquement, ne devrait pas être atteint.
            // Mais pour la robustesse, si nouveauStatut était undefined et qu'on arrive ici avec un statut différent.
            dataToUpdate.statut = validatedData.statut as StatutGarantie;
        }


        if (Object.keys(dataToUpdate).length === 0) {
            return NextResponse.json(garantieActuelle, { status: 200 });
        }
        const updatedGarantie = await prisma.garantie.update({
          where: { id: garantieId },
          data: { ...dataToUpdate, utilisateurModificationId: modifierId === undefined ? null : modifierId },
        });
        return NextResponse.json(updatedGarantie);
      }

    } catch (error: any) {
      console.error(`Erreur PUT /api/garanties/${garantieId}:`, error);
      if (error.message && (error.message.includes("dépasse le disponible") || error.message.includes("Activation impossible"))) {
        return NextResponse.json({ message: error.message }, { status: 400 });
      }
       if (error.message && error.message.includes("non trouvée")) { // Plus générique pour les erreurs "non trouvée"
        return NextResponse.json({ message: error.message }, { status: 404 });
      }
      if (error.code === 'P2025') {
        return NextResponse.json({ message: "Garantie ou entité liée non trouvée lors de la mise à jour." }, { status: 404 });
      }
      return NextResponse.json({ message: "Erreur interne du serveur lors de la mise à jour de la garantie." }, { status: 500 });
    }
  });
}

export async function PATCH(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (
    !session ||
    !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)
  ) {
    return NextResponse.json({ message: "Non autorisé à supprimer cette garantie" }, { status: 403 });
  }

  const garantieId = parseInt(resolvedParams.id);
  if (isNaN(garantieId)) {
    return NextResponse.json({ message: "ID de garantie invalide" }, { status: 400 });
  }

  const modifierId = session.user?.id ? parseInt(session.user.id) : undefined;

  return withAudit(async () => {
    try {
      // Ici, on force le statut à "Supprimee"
      const updatedGarantie = await prisma.garantie.update({
        where: { id: garantieId },
        data: {
          statut: "Supprimee",
          utilisateurModificationId: modifierId,
        },
      });
      return NextResponse.json(updatedGarantie);
    } catch (error: any) {
      console.error(`Erreur PATCH (soft delete) /api/garanties/${garantieId}:`, error);
      if (error.code === 'P2025') return NextResponse.json({ message: "Garantie non trouvée" }, { status: 404 });
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  }, {
    module: 'GARANTIES',
    operation: BusinessAction.GARANTIE_DELETE,
    resourceId: garantieId.toString(),
    metadata: { modifierId }
  });
}

// TODO: Fonction DELETE (ou plutôt des actions spécifiques comme Radier, Demander Mainlevée etc.)