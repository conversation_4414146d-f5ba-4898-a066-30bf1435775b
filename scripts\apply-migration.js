// scripts/apply-migration.js
const { execSync } = require('child_process');

try {
  console.log('Application de la migration...');
  execSync('npx prisma db push', { stdio: 'inherit' });
  
  console.log('Génération du client Prisma...');
  execSync('npx prisma generate', { stdio: 'inherit' });
  
  console.log('Migration appliquée avec succès !');
} catch (error) {
  console.error('Erreur lors de l\'application de la migration:', error.message);
  process.exit(1);
}
