// src/app/api/users/[id]/route.ts
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import prisma from "@/lib/prisma";
import { z } from "zod";

type RouteParams = { params: { id: string } };

// Schema de validation pour la mise à jour du profil
const updateProfileSchema = z.object({
  nom: z.string().min(1, "Le nom est requis").optional(),
  prenom: z.string().min(1, "Le prénom est requis").optional(),
  email: z.string().email("Email invalide").optional(),
});

// GET - Récupérer les informations d'un utilisateur
export async function GET(request: Request, { params }: RouteParams) {
  try {
    const resolvedParams = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Non authentifié" },
        { status: 401 }
      );
    }

const userId = parseInt(resolvedParams.id, 10);
if (isNaN(userId)) {
  return NextResponse.json({ error: "ID utilisateur invalide" }, { status: 400 });
}

    // Vérifier que l'utilisateur peut accéder à ces informations
    // (soit ses propres informations, soit il est administrateur)
    if (parseInt(session.user.id, 10) !== userId && session.user.role !== "Administrateur") {
      return NextResponse.json(
        { error: "Accès non autorisé" },
        { status: 403 }
      );
    }

    const user = await prisma.utilisateur.findUnique({
      where: { id: userId },
      select: {
        id: true,
        nom: true,
        prenom: true,
        email: true,
        role: true,
        estActif: true,
        dateCreation: true,
        dateModification: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "Utilisateur non trouvé" },
        { status: 404 }
      );
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error("Erreur lors de la récupération de l'utilisateur:", error);
    return NextResponse.json(
      { error: "Erreur interne du serveur" },
      { status: 500 }
    );
  }
}

// PATCH - Mettre à jour les informations d'un utilisateur
export async function PATCH(request: Request, { params }: RouteParams) {
  try {
    const resolvedParams = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Non authentifié" },
        { status: 401 }
      );
    }

    const userId = parseInt(resolvedParams.id);

    // Vérifier que l'utilisateur peut modifier ces informations
    // (soit ses propres informations, soit il est administrateur)
    if (session.user.id !== resolvedParams.id && session.user.role !== "Administrateur") {
      return NextResponse.json(
        { error: "Accès non autorisé" },
        { status: 403 }
      );
    }

    const body = await request.json();
    
    // Valider les données
    const validationResult = updateProfileSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: "Données invalides",
          details: validationResult.error.errors 
        },
        { status: 400 }
      );
    }

    const { nom, prenom, email } = validationResult.data;

    // Vérifier si l'email est déjà utilisé par un autre utilisateur
    if (email) {
      const existingUser = await prisma.utilisateur.findFirst({
        where: {
          email,
          id: { not: userId },
        },
      });

      if (existingUser) {
        return NextResponse.json(
          { error: "Cette adresse email est déjà utilisée" },
          { status: 400 }
        );
      }
    }

    // Mettre à jour l'utilisateur
    const updatedUser = await prisma.utilisateur.update({
      where: { id: userId },
      data: {
        ...(nom && { nom }),
        ...(prenom && { prenom }),
        ...(email && { email }),
        dateModification: new Date(),
      },
      select: {
        id: true,
        nom: true,
        prenom: true,
        email: true,
        role: true,
        estActif: true,
        dateCreation: true,
        dateModification: true,
      },
    });

    return NextResponse.json(updatedUser);
  } catch (error) {
    console.error("Erreur lors de la mise à jour de l'utilisateur:", error);
    return NextResponse.json(
      { error: "Erreur interne du serveur" },
      { status: 500 }
    );
  }
}
