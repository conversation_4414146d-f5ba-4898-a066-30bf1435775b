// src/lib/api-utils.ts
// Utility functions for API calls with retry logic for session timing issues

interface ApiCallOptions {
  maxRetries?: number;
  retryDelay?: number;
  retryOn403?: boolean;
}

/**
 * Makes an API call with retry logic for 403 errors (session timing issues)
 * @param url - The API endpoint URL
 * @param options - Fetch options
 * @param apiOptions - Retry configuration
 * @returns Promise<Response>
 */
export async function apiCallWithRetry(
  url: string,
  options: RequestInit = {},
  apiOptions: ApiCallOptions = {}
): Promise<Response> {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    retryOn403 = true
  } = apiOptions;

  let lastError: Error | null = null;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch(url, options);

      // If it's a 403 error and we should retry, and we haven't exceeded max retries
      if (response.status === 403 && retryOn403 && attempt < maxRetries) {
        console.log(`API call to ${url} returned 403, retrying (attempt ${attempt + 1}/${maxRetries})`);
        
        // Exponential backoff: wait longer on each retry
        const delay = retryDelay * (attempt + 1);
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }

      // Return the response (success or non-retryable error)
      return response;

    } catch (error) {
      lastError = error as Error;
      
      // If it's the last attempt, throw the error
      if (attempt === maxRetries) {
        throw lastError;
      }

      // Wait before retrying
      const delay = retryDelay * (attempt + 1);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  // This should never be reached, but just in case
  throw lastError || new Error('API call failed after all retries');
}

/**
 * Convenience function for GET requests with retry logic
 * @param url - The API endpoint URL
 * @param apiOptions - Retry configuration
 * @returns Promise<Response>
 */
export async function apiGet(url: string, apiOptions?: ApiCallOptions): Promise<Response> {
  return apiCallWithRetry(url, { method: 'GET' }, apiOptions);
}

/**
 * Convenience function for POST requests with retry logic
 * @param url - The API endpoint URL
 * @param data - Request body data
 * @param apiOptions - Retry configuration
 * @returns Promise<Response>
 */
export async function apiPost(
  url: string, 
  data: any, 
  apiOptions?: ApiCallOptions
): Promise<Response> {
  return apiCallWithRetry(url, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  }, apiOptions);
}

/**
 * Convenience function for PUT requests with retry logic
 * @param url - The API endpoint URL
 * @param data - Request body data
 * @param apiOptions - Retry configuration
 * @returns Promise<Response>
 */
export async function apiPut(
  url: string, 
  data: any, 
  apiOptions?: ApiCallOptions
): Promise<Response> {
  return apiCallWithRetry(url, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  }, apiOptions);
}

/**
 * Convenience function for DELETE requests with retry logic
 * @param url - The API endpoint URL
 * @param apiOptions - Retry configuration
 * @returns Promise<Response>
 */
export async function apiDelete(url: string, apiOptions?: ApiCallOptions): Promise<Response> {
  return apiCallWithRetry(url, { method: 'DELETE' }, apiOptions);
}
