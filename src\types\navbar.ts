// src/types/navbar.ts

export interface User {
  id: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
  role?: string;
}

export interface NavbarProps {
  user?: User | null;
}

export interface UserMenuProps {
  user: User;
}

export interface GlobalSearchProps {
  onSearch?: (query: string) => void;
  placeholder?: string;
  className?: string;
}

export interface SearchResult {
  id: string;
  title: string;
  type: 'garantie' | 'ligne-garantie' | 'partenaire' | 'client' | 'projet';
  url: string;
  description?: string;
}
