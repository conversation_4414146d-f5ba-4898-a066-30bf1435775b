// src/app/configuration/liaisons-partenaires/page.tsx
"use client";

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Users, Link, Unlink, AlertCircle, CheckCircle } from 'lucide-react';
import { LiaisonDialog } from '@/components/configuration/liaison-dialog';

interface Utilisateur {
  id: number;
  nomUtilisateur: string;
  email: string;
  nom: string;
  prenom: string;
  nomComplet: string;
}

interface Partenaire {
  id: number;
  nom: string;
  typePartenaire: string;
  contact: any;
}

interface Liaison {
  utilisateur: Utilisateur;
  partenaire: Partenaire | null;
  estLie: boolean;
}

interface LiaisonsData {
  liaisons: Liaison[];
  partenairesDisponibles: Partenaire[];
  total: number;
  totalLies: number;
  totalNonLies: number;
}

export default function LiaisonsPartenairesPage() {
  const { data: session } = useSession();
  const [data, setData] = useState<LiaisonsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedUtilisateur, setSelectedUtilisateur] = useState<Utilisateur | null>(null);
  const [actionLoading, setActionLoading] = useState<number | null>(null);

  // Vérifier les permissions
  if (session && !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role)) {
    return (
      <div className="container mx-auto py-8">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Vous n'avez pas les permissions nécessaires pour accéder à cette page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const fetchLiaisons = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/configuration/liaisons-partenaires');
      if (!response.ok) {
        throw new Error('Erreur lors du chargement des liaisons');
      }
      const result = await response.json();
      setData(result);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur inconnue');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLiaisons();
  }, []);

  const handleLier = (utilisateur: Utilisateur) => {
    setSelectedUtilisateur(utilisateur);
    setDialogOpen(true);
  };

  const handleDelier = async (partenaireId: number) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cette liaison ?')) {
      return;
    }

    try {
      setActionLoading(partenaireId);
      const response = await fetch(`/api/configuration/liaisons-partenaires/${partenaireId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erreur lors de la suppression');
      }

      const result = await response.json();
      setSuccess(result.message);
      await fetchLiaisons();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur inconnue');
    } finally {
      setActionLoading(null);
    }
  };

  const handleLiaisonCreated = async () => {
    setDialogOpen(false);
    setSelectedUtilisateur(null);
    await fetchLiaisons();
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Chargement des liaisons...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Liaisons Utilisateurs-Partenaires</h1>
          <p className="text-muted-foreground mt-2">
            Gérez les associations entre les utilisateurs ayant le rôle "Partenaire" et les enregistrements partenaires.
          </p>
        </div>
      </div>

      {/* Messages d'état */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {/* Statistiques */}
      {data && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Utilisateurs</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.total}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Utilisateurs Liés</CardTitle>
              <Link className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{data.totalLies}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Utilisateurs Non Liés</CardTitle>
              <Unlink className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{data.totalNonLies}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Partenaires Disponibles</CardTitle>
              <Users className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{data.partenairesDisponibles.length}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Tableau des liaisons */}
      {data && (
        <Card>
          <CardHeader>
            <CardTitle>Gestion des Liaisons</CardTitle>
            <CardDescription>
              Liste des utilisateurs partenaires et leurs associations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-4 font-medium">Utilisateur</th>
                    <th className="text-left p-4 font-medium">Email</th>
                    <th className="text-left p-4 font-medium">Partenaire Associé</th>
                    <th className="text-left p-4 font-medium">Statut</th>
                    <th className="text-left p-4 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {data.liaisons.map((liaison) => (
                    <tr key={liaison.utilisateur.id} className="border-b hover:bg-muted/50">
                      <td className="p-4">
                        <div>
                          <div className="font-medium">{liaison.utilisateur.nomComplet}</div>
                          <div className="text-sm text-muted-foreground">
                            @{liaison.utilisateur.nomUtilisateur}
                          </div>
                        </div>
                      </td>
                      <td className="p-4 text-sm">{liaison.utilisateur.email}</td>
                      <td className="p-4">
                        {liaison.partenaire ? (
                          <div>
                            <div className="font-medium">{liaison.partenaire.nom}</div>
                            <div className="text-sm text-muted-foreground">
                              {liaison.partenaire.typePartenaire}
                            </div>
                          </div>
                        ) : (
                          <span className="text-muted-foreground italic">Aucun partenaire associé</span>
                        )}
                      </td>
                      <td className="p-4">
                        <Badge variant={liaison.estLie ? "default" : "secondary"}>
                          {liaison.estLie ? "Lié" : "Non lié"}
                        </Badge>
                      </td>
                      <td className="p-4">
                        {liaison.estLie ? (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelier(liaison.partenaire!.id)}
                            disabled={actionLoading === liaison.partenaire!.id}
                          >
                            {actionLoading === liaison.partenaire!.id ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Unlink className="h-4 w-4" />
                            )}
                            <span className="ml-2">Délier</span>
                          </Button>
                        ) : (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleLier(liaison.utilisateur)}
                            disabled={data.partenairesDisponibles.length === 0}
                          >
                            <Link className="h-4 w-4" />
                            <span className="ml-2">Lier</span>
                          </Button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Dialog de liaison */}
      <LiaisonDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        utilisateur={selectedUtilisateur}
        partenairesDisponibles={data?.partenairesDisponibles || []}
        onSuccess={handleLiaisonCreated}
        onError={setError}
      />
    </div>
  );
}
