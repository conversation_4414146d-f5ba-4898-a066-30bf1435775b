"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// scripts/create-partner-user.ts
const client_1 = require("@prisma/client");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const prisma = new client_1.PrismaClient();
async function main() {
    const partnerEmail = '<EMAIL>';
    const partnerPassword = 'Partner123!';
    try {
        // Vérifier si l'utilisateur partenaire existe déjà
        const existingUser = await prisma.utilisateur.findUnique({
            where: { email: partnerEmail }
        });
        if (existingUser) {
            console.log(`L'utilisateur partenaire ${partnerEmail} existe déjà.`);
            return;
        }
        // Créer l'utilisateur partenaire
        const hashedPassword = await bcryptjs_1.default.hash(partnerPassword, 10);
        const partnerUser = await prisma.utilisateur.create({
            data: {
                nomUtilisateur: 'partenaire_test',
                email: partnerEmail,
                motDePasse: hashedPassword,
                nom: 'Test',
                prenom: 'Partenaire',
                role: client_1.RoleUtilisateur.Partenaire,
                estActif: true,
            },
        });
        console.log(`Utilisateur partenaire créé: ${partnerUser.email}`);
        // Créer l'enregistrement partenaire associé avec la nouvelle liaison
        const partenaire = await prisma.partenaire.create({
            data: {
                nom: 'Banque Test Partenaire',
                typePartenaire: client_1.TypePartenaire.Banque,
                description: 'Banque de test pour les fonctionnalités partenaire',
                contact: {
                    nomRepresentant: 'Partenaire Test',
                    email: partnerEmail,
                    telephone: '+221 77 123 45 67',
                    adresse: '123 Rue Test, Dakar'
                },
                convention: 'CONV-TEST-001',
                utilisateurCreationId: 1, // Créé par l'admin
                utilisateurPartenaireId: partnerUser.id, // Nouvelle liaison directe
            },
        });
        console.log(`Partenaire créé: ${partenaire.nom} (ID: ${partenaire.id})`);
        console.log(`Liaison établie via utilisateurPartenaireId: ${partnerUser.id}`);
        console.log(`Utilisateur: ${partnerUser.email}`);
        console.log(`Mot de passe: ${partnerPassword}`);
    }
    catch (error) {
        console.error('Erreur lors de la création du partenaire de test:', error);
    }
    finally {
        await prisma.$disconnect();
    }
}
main();
