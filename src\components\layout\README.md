# Layout Components

This directory contains the main layout components for the GesGar application.

## Components

### Navbar (`navbar.tsx`)
The main horizontal navigation bar component that appears at the top of the application.

**Features:**
- Fixed positioning at the top of the page
- Responsive design that adapts to different screen sizes
- Integrates with NextAuth session management
- Contains global search and user menu

**Usage:**
```tsx
import { NavbarWithSession } from "@/components/layout/navbar";

// Use the session-aware version (recommended)
<NavbarWithSession />

// Or pass user data manually
<Navbar user={userData} />
```

### GlobalSearch (`global-search.tsx`)
A responsive search component that adapts to different screen sizes.

**Features:**
- Desktop: Always visible search bar
- Mobile: Expandable search icon that reveals input field
- Search submission handling
- Clear search functionality

**Usage:**
```tsx
import { GlobalSearch } from "@/components/layout/global-search";

<GlobalSearch 
  onSearch={(query) => handleSearch(query)}
  placeholder="Rechercher..."
/>
```

### UserMenu (`user-menu.tsx`)
A dropdown menu component for user profile and account actions.

**Features:**
- User avatar with fallback to initials
- Welcome message with user name
- Dropdown menu with profile, settings, and logout options
- Responsive design that hides user name on small screens

**Usage:**
```tsx
import { UserMenu } from "@/components/layout/user-menu";

<UserMenu user={userData} />
```

## Integration

The navbar is integrated into the main layout (`src/app/layout.tsx`) and appears on all authenticated pages. It works alongside the existing sidebar navigation.

## Styling

All components use Tailwind CSS classes and follow the existing design system:
- Uses CSS variables for theming
- Supports light/dark mode
- Consistent with shadcn/ui component library
- Responsive breakpoints: sm (640px), md (768px), lg (1024px)

## TypeScript

Type definitions are available in `src/types/navbar.ts` for all component props and interfaces.
