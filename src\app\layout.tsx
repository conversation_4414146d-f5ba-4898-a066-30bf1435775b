// src/app/layout.tsx
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Providers from "./providers";
import { Toaster } from "@/components/ui/sonner"; // Importer Toaster
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { FileSignature, Handshake, ShieldCheck, ListChecks, AlertTriangle } from "lucide-react";
import { headers } from "next/headers";
import { NavbarWithSession } from "@/components/layout/navbar";

const inter = Inter({ subsets: ["latin"] });

// Liens de navigation pour l'administration (réservés aux administrateurs)
const adminNavLinks = [
  { href: "/dashboard", label: "Tableau de Bord", icon: <span className="mr-3">📊</span> },
  { href: "/admin/utilisateurs", label: "Utilisateurs", icon: <span className="mr-3">👥</span> },
  { href: "/admin/audit-log", label: "Journal d'Audit", icon: <span className="mr-3">📝</span> },
  { href: "/admin/system-settings", label: "Paramètres Système", icon: <span className="mr-3">⚙️</span> },
];

// Liens de navigation pour la gestion opérationnelle (visibles par les profils internes)
const operationalNavLinks = [
  { href: "/lignes-garantie", label: "Lignes de Garantie", icon: <FileSignature className="mr-3 w-5 h-5" /> },
  { href: "/allocations", label: "Allocations Partenaires", icon: <Handshake className="mr-3 w-5 h-5" /> },
  { href: "/garanties", label: "Garanties", icon: <ShieldCheck className="mr-3 w-5 h-5" /> },
  { href: "/mainlevees", label: "Mainlevées", icon: <ListChecks className="mr-3 w-5 h-5" /> },
  { href: "/mises-en-jeu", label: "Mises en Jeu", icon: <AlertTriangle className="mr-3 w-5 h-5" /> },
];

// Liens de navigation pour les partenaires (données filtrées)
const partnerNavLinks = [
  { href: "/allocations", label: "Allocations Partenaires", icon: <Handshake className="mr-3 w-5 h-5" /> },
  { href: "/garanties", label: "Garanties", icon: <ShieldCheck className="mr-3 w-5 h-5" /> },
  { href: "/mainlevees", label: "Mainlevées", icon: <ListChecks className="mr-3 w-5 h-5" /> },
  { href: "/mises-en-jeu", label: "Mises en Jeu", icon: <AlertTriangle className="mr-3 w-5 h-5" /> },
];

// Liens de configuration (visibles par les profils internes et administrateurs)
const configurationNavLinks = [
  { href: "/configuration/bailleurs", label: "Bailleurs de Fonds", icon: <span className="mr-3">🏛️</span> },
  { href: "/configuration/partenaires", label: "Partenaires Financiers", icon: <span className="mr-3">🏢</span> },
  { href: "/configuration/clients-beneficiaires", label: "Clients Bénéficiaires", icon: <span className="mr-3">👤</span> },
  { href: "/configuration/secteurs-activite", label: "Secteurs d'Activité", icon: <span className="mr-3">🏷️</span> },
  { href: "/configuration/projets", label: "Projets", icon: <span className="mr-3">📁</span> },
  { href: "/configuration/liaisons-partenaires", label: "Liaison Utilisateurs-Partenaires", icon: <span className="mr-3">🔗</span> },
];

// Définition des rôles internes (qui peuvent voir toutes les options sauf Administration)
const INTERNAL_ROLES = ["GestionnaireGesGar", "AnalysteFinancier", "Auditeur"];

export const metadata: Metadata = {
  title: "GesGar",
  description: "Gestion des Lignes de Garantie",
};

// Use environment-aware logging
if (process.env.NODE_ENV === 'development') {
  console.log("[RootLayout] Rendering on server...");
}
export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getServerSession(authOptions);
  const headersList = await headers();
  const pathname = headersList.get("x-invoke-path") || headersList.get("x-pathname") || "";
  const isAuthPage = pathname.startsWith("/auth/");

  return (
    <html lang="fr">
      <body className={inter.className}>
        <Providers>
          {!isAuthPage && session ? (
            // Layout avec navbar et sidebar pour les utilisateurs connectés
            <div className="min-h-screen bg-muted/40">
              {/* Horizontal Navbar */}
              <NavbarWithSession />

              {/* Main layout with sidebar */}
              <div className="flex">
                {/* Sidebar for Configuration & Admin */}
                <aside className="hidden md:flex md:flex-col w-64 bg-background border-r">
                <div className="p-4 border-b">
                  <h1 className="text-2xl font-semibold text-primary">GesGar</h1>
                </div>
                <nav className="flex-1 p-4 space-y-2">
                  {/* Section Administration - Visible uniquement pour les Administrateurs */}
                  {session?.user?.role === "Administrateur" && (
                    <div>
                      <h2 className="px-3 py-2 text-xs font-semibold uppercase text-muted-foreground tracking-wider">
                        Administration
                      </h2>
                      <div className="space-y-1">
                        {adminNavLinks.map(link => (
                          <a key={link.href} href={link.href} className="flex items-center px-3 py-2 text-muted-foreground rounded-lg hover:bg-accent hover:text-accent-foreground">
                            {link.icon}
                            {link.label}
                          </a>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Section Gestion Opérationnelle - Visible pour les profils internes et administrateurs */}
                  {(session?.user?.role === "Administrateur" || INTERNAL_ROLES.includes(session?.user?.role as string)) && (
                    <div>
                      <h2 className="px-3 py-2 text-xs font-semibold uppercase text-muted-foreground tracking-wider">
                        Gestion des Garanties
                      </h2>
                      <div className="space-y-1">
                        {operationalNavLinks.map(link => (
                          <a key={link.href} href={link.href} className="flex items-center px-3 py-2 text-muted-foreground rounded-lg hover:bg-accent hover:text-accent-foreground">
                            {link.icon}
                            {link.label}
                          </a>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Section Configuration - Visible pour les profils internes et administrateurs */}
                  {(session?.user?.role === "Administrateur" || INTERNAL_ROLES.includes(session?.user?.role as string)) && (
                    <div>
                      <h2 className="px-3 py-2 text-xs font-semibold uppercase text-muted-foreground tracking-wider">
                        Configuration
                      </h2>
                      <div className="space-y-1">
                        {configurationNavLinks.map(link => (
                          <a key={link.href} href={link.href} className="flex items-center px-3 py-2 text-muted-foreground rounded-lg hover:bg-accent hover:text-accent-foreground">
                            {link.icon}
                            {link.label}
                          </a>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Section Partenaire - Visible uniquement pour les partenaires */}
                  {session?.user?.role === "Partenaire" && (
                    <div>
                      <h2 className="px-3 py-2 text-xs font-semibold uppercase text-muted-foreground tracking-wider">
                        Mes Garanties
                      </h2>
                      <div className="space-y-1">
                        {partnerNavLinks.map(link => (
                          <a key={link.href} href={link.href} className="flex items-center px-3 py-2 text-muted-foreground rounded-lg hover:bg-accent hover:text-accent-foreground">
                            {link.icon}
                            {link.label}
                          </a>
                        ))}
                      </div>
                    </div>
                  )}
                </nav>
                <div className="p-4 border-t">
                  <p className="text-xs text-muted-foreground">
                    © GesGar {new Date().getFullYear()}
                  </p>
                </div>
              </aside>
                {/* Main content */}
                <div className="flex-1 pt-16">
                  {children}
                </div>
              </div>
            </div>
          ) : (
            // Layout simple pour les pages d'authentification
            <div className="min-h-screen flex items-center justify-center bg-white">
              {children}
            </div>
          )}
          <Toaster />
        </Providers>
      </body>
    </html>
  );
}