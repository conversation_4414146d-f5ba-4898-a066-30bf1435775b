// src/components/test/sidebar-permissions-test.tsx
"use client";

import React from 'react';
import { RoleUtilisateur } from '@prisma/client';

// Réplication des constantes du layout pour les tests
const INTERNAL_ROLES = ["GestionnaireGesGar", "AnalysteFinancier", "Auditeur"];

const adminNavLinks = [
  { href: "/dashboard", label: "Tableau de Bord" },
  { href: "/admin/utilisateurs", label: "Utilisateurs" },
  { href: "/admin/audit-log", label: "Journal d'Audit" },
  { href: "/admin/system-settings", label: "Paramètres Système" },
];

const operationalNavLinks = [
  { href: "/lignes-garantie", label: "Lignes de Garantie" },
  { href: "/allocations", label: "Allocations Partenaires" },
  { href: "/garanties", label: "Garanties" },
  { href: "/mainlevees", label: "Mainlevées" },
  { href: "/mises-en-jeu", label: "Mises en Jeu" },
];

const partnerNavLinks = [
  { href: "/allocations", label: "Allocations Partenaires" },
  { href: "/garanties", label: "Garanties" },
  { href: "/mainlevees", label: "Mainlevées" },
  { href: "/mises-en-jeu", label: "Mises en Jeu" },
];

const configurationNavLinks = [
  { href: "/configuration/bailleurs", label: "Bailleurs de Fonds" },
  { href: "/configuration/partenaires", label: "Partenaires Financiers" },
  { href: "/configuration/clients-beneficiaires", label: "Clients Bénéficiaires" },
  { href: "/configuration/secteurs-activite", label: "Secteurs d'Activité" },
  { href: "/configuration/projets", label: "Projets" },
];

interface SidebarPermissionsTestProps {
  userRole: string;
}

export function SidebarPermissionsTest({ userRole }: SidebarPermissionsTestProps) {
  const isAdmin = userRole === "Administrateur";
  const isInternal = INTERNAL_ROLES.includes(userRole);
  const isPartner = userRole === "Partenaire";
  const isBailleur = userRole === "Bailleur";

  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">Test des Permissions du Sidebar</h2>
      <div className="mb-4">
        <span className="font-semibold">Rôle testé: </span>
        <span className={`px-2 py-1 rounded text-sm ${
          isAdmin ? 'bg-red-100 text-red-800' :
          isInternal ? 'bg-blue-100 text-blue-800' :
          isPartner ? 'bg-green-100 text-green-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {userRole}
        </span>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Section Administration */}
        <div className="border rounded-lg p-4">
          <h3 className="font-semibold mb-2 text-red-600">Administration</h3>
          <p className="text-sm text-gray-600 mb-2">
            Visible: {isAdmin ? "✅ Oui" : "❌ Non"}
          </p>
          {isAdmin && (
            <ul className="space-y-1">
              {adminNavLinks.map(link => (
                <li key={link.href} className="text-sm text-gray-700">
                  • {link.label}
                </li>
              ))}
            </ul>
          )}
          {!isAdmin && (
            <p className="text-sm text-gray-500 italic">Section masquée</p>
          )}
        </div>

        {/* Section Gestion Opérationnelle */}
        <div className="border rounded-lg p-4">
          <h3 className="font-semibold mb-2 text-blue-600">Gestion des Garanties</h3>
          <p className="text-sm text-gray-600 mb-2">
            Visible: {(isAdmin || isInternal) ? "✅ Oui" : "❌ Non"}
          </p>
          {(isAdmin || isInternal) && (
            <ul className="space-y-1">
              {operationalNavLinks.map(link => (
                <li key={link.href} className="text-sm text-gray-700">
                  • {link.label}
                </li>
              ))}
            </ul>
          )}
          {!(isAdmin || isInternal) && (
            <p className="text-sm text-gray-500 italic">Section masquée</p>
          )}
        </div>

        {/* Section Configuration */}
        <div className="border rounded-lg p-4">
          <h3 className="font-semibold mb-2 text-green-600">Configuration</h3>
          <p className="text-sm text-gray-600 mb-2">
            Visible: {(isAdmin || isInternal) ? "✅ Oui" : "❌ Non"}
          </p>
          {(isAdmin || isInternal) && (
            <ul className="space-y-1">
              {configurationNavLinks.map(link => (
                <li key={link.href} className="text-sm text-gray-700">
                  • {link.label}
                </li>
              ))}
            </ul>
          )}
          {!(isAdmin || isInternal) && (
            <p className="text-sm text-gray-500 italic">Section masquée</p>
          )}
        </div>

        {/* Section Partenaire */}
        <div className="border rounded-lg p-4">
          <h3 className="font-semibold mb-2 text-purple-600">Mes Garanties (Partenaire)</h3>
          <p className="text-sm text-gray-600 mb-2">
            Visible: {isPartner ? "✅ Oui" : "❌ Non"}
          </p>
          {isPartner && (
            <ul className="space-y-1">
              {partnerNavLinks.map(link => (
                <li key={link.href} className="text-sm text-gray-700">
                  • {link.label}
                </li>
              ))}
            </ul>
          )}
          {!isPartner && (
            <p className="text-sm text-gray-500 italic">Section masquée</p>
          )}
        </div>
      </div>

      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-semibold mb-2">Résumé des permissions:</h4>
        <ul className="text-sm space-y-1">
          <li>
            <strong>Administrateur:</strong> Voit tout (Administration + Gestion + Configuration)
          </li>
          <li>
            <strong>Profils internes</strong> (GestionnaireGesGar, AnalysteFinancier, Auditeur):
            Voient Gestion + Configuration (pas Administration)
          </li>
          <li>
            <strong>Partenaire:</strong> Voit uniquement "Mes Garanties" avec ses propres données filtrées
          </li>
          <li>
            <strong>Bailleur:</strong> Ne voit aucune section du sidebar
          </li>
        </ul>
      </div>
    </div>
  );
}

// Composant pour tester tous les rôles
export function AllRolesTest() {
  const allRoles = Object.values(RoleUtilisateur);

  return (
    <div className="space-y-8">
      <h1 className="text-2xl font-bold text-center mb-8">
        Test des Permissions du Sidebar pour Tous les Rôles
      </h1>
      {allRoles.map(role => (
        <SidebarPermissionsTest key={role} userRole={role} />
      ))}
    </div>
  );
}
