// src/components/configuration/liaison-dialog.tsx
"use client";

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Loader2, Link } from 'lucide-react';

interface Utilisateur {
  id: number;
  nomUtilisateur: string;
  email: string;
  nom: string;
  prenom: string;
  nomComplet: string;
}

interface Partenaire {
  id: number;
  nom: string;
  typePartenaire: string;
  contact: any;
}

interface LiaisonDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  utilisateur: Utilisateur | null;
  partenairesDisponibles: Partenaire[];
  onSuccess: () => void;
  onError: (error: string) => void;
}

export function LiaisonDialog({
  open,
  onOpenChange,
  utilisateur,
  partenairesDisponibles,
  onSuccess,
  onError
}: LiaisonDialogProps) {
  const [selectedPartenaireId, setSelectedPartenaireId] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    if (!utilisateur || !selectedPartenaireId) {
      onError('Veuillez sélectionner un partenaire');
      return;
    }

    try {
      setLoading(true);
      const response = await fetch('/api/configuration/liaisons-partenaires', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          utilisateurId: utilisateur.id,
          partenaireId: parseInt(selectedPartenaireId),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erreur lors de la création de la liaison');
      }

      const result = await response.json();
      onSuccess();
      setSelectedPartenaireId('');
    } catch (err) {
      onError(err instanceof Error ? err.message : 'Erreur inconnue');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setSelectedPartenaireId('');
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Link className="h-5 w-5 mr-2" />
            Créer une Liaison Utilisateur-Partenaire
          </DialogTitle>
          <DialogDescription>
            Associez l'utilisateur sélectionné à un partenaire disponible.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Informations utilisateur */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Utilisateur sélectionné</Label>
            <div className="p-3 bg-muted rounded-lg">
              {utilisateur && (
                <div>
                  <div className="font-medium">{utilisateur.nomComplet}</div>
                  <div className="text-sm text-muted-foreground">
                    @{utilisateur.nomUtilisateur} • {utilisateur.email}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Sélection du partenaire */}
          <div className="space-y-2">
            <Label htmlFor="partenaire-select" className="text-sm font-medium">
              Partenaire à associer
            </Label>
            {partenairesDisponibles.length > 0 ? (
              <Select
                value={selectedPartenaireId}
                onValueChange={setSelectedPartenaireId}
              >
                <SelectTrigger id="partenaire-select">
                  <SelectValue placeholder="Sélectionnez un partenaire..." />
                </SelectTrigger>
                <SelectContent>
                  {partenairesDisponibles.map((partenaire) => (
                    <SelectItem key={partenaire.id} value={partenaire.id.toString()}>
                      <div className="flex flex-col">
                        <span className="font-medium">{partenaire.nom}</span>
                        <span className="text-sm text-muted-foreground">
                          {partenaire.typePartenaire}
                          {partenaire.contact?.email && ` • ${partenaire.contact.email}`}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : (
              <div className="p-3 bg-muted rounded-lg text-center text-muted-foreground">
                Aucun partenaire disponible pour la liaison.
                <br />
                Tous les partenaires sont déjà associés à des utilisateurs.
              </div>
            )}
          </div>

          {/* Information sur la liaison */}
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="text-sm text-blue-800">
              <strong>Note :</strong> Une fois la liaison créée, l'utilisateur pourra accéder uniquement 
              aux données (allocations, garanties, mainlevées, mises en jeu) associées au partenaire sélectionné.
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={loading}
          >
            Annuler
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading || !selectedPartenaireId || partenairesDisponibles.length === 0}
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Création...
              </>
            ) : (
              <>
                <Link className="h-4 w-4 mr-2" />
                Créer la Liaison
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
