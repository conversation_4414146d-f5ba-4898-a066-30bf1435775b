// scripts/test-apis.js
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function testAPI(endpoint, method = 'GET', body = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (body) {
      options.body = JSON.stringify(body);
    }

    const response = await fetch(`${BASE_URL}${endpoint}`, options);
    const data = await response.json();
    
    console.log(`\n${method} ${endpoint}`);
    console.log(`Status: ${response.status}`);
    console.log(`Response:`, JSON.stringify(data, null, 2));
    
    return { status: response.status, data };
  } catch (error) {
    console.error(`Erreur lors du test de ${endpoint}:`, error.message);
    return { error: error.message };
  }
}

async function runTests() {
  console.log('🧪 Test des APIs de Liaison Utilisateur-Partenaire');
  console.log('=' .repeat(60));

  // Test 1: GET liaisons (sans authentification - devrait retourner 403)
  await testAPI('/api/configuration/liaisons-partenaires');

  // Test 2: POST liaison (sans authentification - devrait retourner 403)
  await testAPI('/api/configuration/liaisons-partenaires', 'POST', {
    utilisateurId: 1,
    partenaireId: 1
  });

  // Test 3: DELETE liaison (sans authentification - devrait retourner 403)
  await testAPI('/api/configuration/liaisons-partenaires/1', 'DELETE');

  console.log('\n✅ Tests terminés');
  console.log('Note: Les erreurs 403 sont normales car aucune authentification n\'est fournie.');
  console.log('Pour tester avec authentification, utilisez l\'interface web.');
}

if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testAPI, runTests };
