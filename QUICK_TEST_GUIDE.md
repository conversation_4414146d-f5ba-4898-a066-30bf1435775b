# Guide de Test Rapide - Interface de Gestion des Liaisons

## 🚀 Test Immédiat (Sans Migration)

### Interface de Démonstration

Vous pouvez tester l'interface immédiatement sans appliquer la migration :

**URL de démonstration :** `/configuration/liaisons-partenaires/demo`

Cette page contient :
- ✅ Interface complète avec données mockées
- ✅ Statistiques en temps réel
- ✅ Tableau interactif
- ✅ Actions de liaison/déliaison (simulation)
- ✅ Messages de feedback

### Fonctionnalités Visibles

1. **Statistiques** : 4 cartes avec métriques
2. **Tableau principal** : Liste des utilisateurs partenaires
3. **Actions** : Boutons Lier/Délier fonctionnels
4. **Partenaires disponibles** : Liste des partenaires non liés
5. **Messages** : Alerts de succès/erreur

## 🔧 Test Complet (Avec Migration)

### Étape 1: Appliquer la Migration

```bash
# Appliquer les changements de schéma
node scripts/apply-migration.js
```

### Étape 2: Créer des Données de Test

```bash
# Créer un utilisateur partenaire avec liaison
node scripts/run-create-partner.js
```

### Étape 3: Tester l'Interface Complète

**URL complète :** `/configuration/liaisons-partenaires`

**Identifiants admin pour tester :**
- Email : `<EMAIL>`
- Mot de passe : `Admin123!`

**Identifiants partenaire pour tester le filtrage :**
- Email : `<EMAIL>`
- Mot de passe : `Partner123!`

## 📋 Checklist de Test

### Interface de Démonstration ✅

- [ ] Accéder à `/configuration/liaisons-partenaires/demo`
- [ ] Vérifier l'affichage des statistiques
- [ ] Tester les boutons Lier/Délier
- [ ] Vérifier les messages de feedback
- [ ] Vérifier la responsivité mobile

### Interface Complète (après migration)

- [ ] Se connecter en tant qu'administrateur
- [ ] Accéder à `/configuration/liaisons-partenaires`
- [ ] Vérifier le chargement des données réelles
- [ ] Créer une nouvelle liaison
- [ ] Supprimer une liaison existante
- [ ] Vérifier les contraintes d'unicité

### Filtrage Partenaire

- [ ] Se connecter en tant qu'utilisateur partenaire
- [ ] Vérifier le sidebar "Mes Garanties"
- [ ] Tester l'accès aux APIs :
  - [ ] `/api/allocations`
  - [ ] `/api/garanties`
  - [ ] `/api/mainlevees`
  - [ ] `/api/mises-en-jeu`

### Sécurité

- [ ] Tenter d'accéder à l'interface avec un rôle non autorisé
- [ ] Vérifier les messages d'erreur 403
- [ ] Tester les contraintes de validation

## 🎯 Résultats Attendus

### Interface de Démonstration

- **Affichage** : Interface complète et responsive
- **Interactions** : Boutons fonctionnels avec feedback
- **Design** : Cohérent avec le reste de l'application

### Interface Complète

- **Données** : Chargement depuis la base de données
- **CRUD** : Création/suppression de liaisons
- **Validation** : Contraintes respectées
- **Sécurité** : Accès restreint aux rôles autorisés

### Filtrage Partenaire

- **Sidebar** : Section "Mes Garanties" visible
- **APIs** : Données filtrées par partenaire
- **Sécurité** : Accès uniquement aux données associées

## 🐛 Dépannage

### Erreurs Communes

1. **Module not found** : Vérifier que tous les composants UI existent
2. **Database error** : S'assurer que la migration est appliquée
3. **403 Forbidden** : Vérifier les rôles utilisateur
4. **Empty data** : Créer des données de test

### Logs à Vérifier

- **Console navigateur** : Erreurs JavaScript
- **Logs serveur** : Erreurs API et base de données
- **Network tab** : Requêtes API et réponses

## 📞 Support

En cas de problème :

1. **Vérifier les logs** dans la console
2. **Tester la démo** d'abord pour isoler le problème
3. **Vérifier la migration** si les APIs échouent
4. **Consulter le guide de déploiement** pour plus de détails

## 🎉 Validation Finale

L'interface est considérée comme fonctionnelle si :

- ✅ La démo s'affiche correctement
- ✅ L'interface complète charge les données
- ✅ Les liaisons peuvent être créées/supprimées
- ✅ Le filtrage partenaire fonctionne
- ✅ La sécurité est respectée

## 🔗 Liens Utiles

- **Démo** : `/configuration/liaisons-partenaires/demo`
- **Interface** : `/configuration/liaisons-partenaires`
- **API Test** : `/api/configuration/liaisons-partenaires`
- **Documentation** : `DEPLOYMENT_GUIDE.md`
