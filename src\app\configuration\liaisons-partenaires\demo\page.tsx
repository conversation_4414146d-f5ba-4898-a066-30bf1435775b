// src/app/configuration/liaisons-partenaires/demo/page.tsx
"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Users, Link, Unlink, AlertCircle, CheckCircle } from 'lucide-react';

// Données de démonstration
const mockData = {
  liaisons: [
    {
      utilisateur: {
        id: 1,
        nomUtilisateur: 'partenaire1',
        email: '<EMAIL>',
        nom: '<PERSON><PERSON>',
        prenom: '<PERSON>',
        nomComplet: '<PERSON>'
      },
      partenaire: {
        id: 1,
        nom: 'Banque Atlantique',
        typePartenaire: 'Banque',
        contact: { email: '<EMAIL>' }
      },
      estLie: true
    },
    {
      utilisateur: {
        id: 2,
        nomUtilisateur: 'partenaire2',
        email: '<EMAIL>',
        nom: '<PERSON>',
        prenom: '<PERSON>',
        nomComplet: '<PERSON>'
      },
      partenaire: null,
      estLie: false
    },
    {
      utilisateur: {
        id: 3,
        nomUtilisateur: 'partenaire3',
        email: '<EMAIL>',
        nom: 'Diallo',
        prenom: 'Amadou',
        nomComplet: 'Amadou Diallo'
      },
      partenaire: {
        id: 2,
        nom: 'CBAO Groupe Attijariwafa Bank',
        typePartenaire: 'Banque',
        contact: { email: '<EMAIL>' }
      },
      estLie: true
    }
  ],
  partenairesDisponibles: [
    {
      id: 3,
      nom: 'Ecobank Sénégal',
      typePartenaire: 'Banque',
      contact: { email: '<EMAIL>' }
    },
    {
      id: 4,
      nom: 'PAMECAS',
      typePartenaire: 'IMF',
      contact: { email: '<EMAIL>' }
    }
  ],
  total: 3,
  totalLies: 2,
  totalNonLies: 1
};

export default function LiaisonsPartenairesDemo() {
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleAction = (action: string, item: any) => {
    if (action === 'lier') {
      setSuccess(`Liaison créée pour ${item.nomComplet}`);
    } else {
      setSuccess(`Liaison supprimée pour ${item.nomComplet}`);
    }
    setTimeout(() => setSuccess(null), 3000);
  };

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Liaisons Utilisateurs-Partenaires (Démo)</h1>
          <p className="text-muted-foreground mt-2">
            Interface de démonstration pour la gestion des associations entre utilisateurs partenaires et enregistrements partenaires.
          </p>
        </div>
      </div>

      {/* Messages d'état */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Utilisateurs</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockData.total}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Utilisateurs Liés</CardTitle>
            <Link className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{mockData.totalLies}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Utilisateurs Non Liés</CardTitle>
            <Unlink className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{mockData.totalNonLies}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Partenaires Disponibles</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{mockData.partenairesDisponibles.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Tableau des liaisons */}
      <Card>
        <CardHeader>
          <CardTitle>Gestion des Liaisons (Démo)</CardTitle>
          <CardDescription>
            Liste des utilisateurs partenaires et leurs associations - Interface de démonstration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-4 font-medium">Utilisateur</th>
                  <th className="text-left p-4 font-medium">Email</th>
                  <th className="text-left p-4 font-medium">Partenaire Associé</th>
                  <th className="text-left p-4 font-medium">Statut</th>
                  <th className="text-left p-4 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {mockData.liaisons.map((liaison) => (
                  <tr key={liaison.utilisateur.id} className="border-b hover:bg-muted/50">
                    <td className="p-4">
                      <div>
                        <div className="font-medium">{liaison.utilisateur.nomComplet}</div>
                        <div className="text-sm text-muted-foreground">
                          @{liaison.utilisateur.nomUtilisateur}
                        </div>
                      </div>
                    </td>
                    <td className="p-4 text-sm">{liaison.utilisateur.email}</td>
                    <td className="p-4">
                      {liaison.partenaire ? (
                        <div>
                          <div className="font-medium">{liaison.partenaire.nom}</div>
                          <div className="text-sm text-muted-foreground">
                            {liaison.partenaire.typePartenaire}
                          </div>
                        </div>
                      ) : (
                        <span className="text-muted-foreground italic">Aucun partenaire associé</span>
                      )}
                    </td>
                    <td className="p-4">
                      <Badge variant={liaison.estLie ? "default" : "secondary"}>
                        {liaison.estLie ? "Lié" : "Non lié"}
                      </Badge>
                    </td>
                    <td className="p-4">
                      {liaison.estLie ? (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleAction('delier', liaison.utilisateur)}
                        >
                          <Unlink className="h-4 w-4" />
                          <span className="ml-2">Délier</span>
                        </Button>
                      ) : (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleAction('lier', liaison.utilisateur)}
                        >
                          <Link className="h-4 w-4" />
                          <span className="ml-2">Lier</span>
                        </Button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Partenaires disponibles */}
      <Card>
        <CardHeader>
          <CardTitle>Partenaires Disponibles pour Liaison</CardTitle>
          <CardDescription>
            Liste des partenaires qui peuvent être associés à des utilisateurs
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {mockData.partenairesDisponibles.map((partenaire) => (
              <div key={partenaire.id} className="p-4 border rounded-lg">
                <div className="font-medium">{partenaire.nom}</div>
                <div className="text-sm text-muted-foreground">{partenaire.typePartenaire}</div>
                <div className="text-sm text-muted-foreground">{partenaire.contact.email}</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>Note :</strong> Ceci est une interface de démonstration. 
          Pour utiliser la version complète, appliquez d'abord la migration de base de données 
          et accédez à <code>/configuration/liaisons-partenaires</code>.
        </AlertDescription>
      </Alert>
    </div>
  );
}
