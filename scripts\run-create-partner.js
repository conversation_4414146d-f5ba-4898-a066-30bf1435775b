// scripts/run-create-partner.js
const { execSync } = require('child_process');
const path = require('path');

try {
  console.log('Compilation du script TypeScript...');
  execSync('npx tsc scripts/create-partner-user.ts --outDir dist --target es2020 --module commonjs --esModuleInterop --skipLibCheck', { stdio: 'inherit' });
  
  console.log('Exécution du script...');
  execSync('node dist/create-partner-user.js', { stdio: 'inherit' });
  
  console.log('Script exécuté avec succès !');
} catch (error) {
  console.error('Erreur lors de l\'exécution:', error.message);
  process.exit(1);
}
