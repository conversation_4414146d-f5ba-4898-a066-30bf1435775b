# Guide de Déploiement - Interface de Gestion des Liaisons Utilisateur-Partenaire

## Vue d'ensemble

Cette mise à jour introduit une interface complète de gestion des liaisons entre les utilisateurs ayant le rôle "Partenaire" et les enregistrements de la table Partenaire.

## Modifications Apportées

### 1. Schéma de Base de Données

**Nouveau champ ajouté à la table `Partenaire` :**
```sql
ALTER TABLE "Partenaire" ADD COLUMN "utilisateurPartenaireId" INTEGER;
ALTER TABLE "Partenaire" ADD CONSTRAINT "Partenaire_utilisateurPartenaireId_fkey" 
FOREIGN KEY ("utilisateurPartenaireId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "Partenaire" ADD CONSTRAINT "Partenaire_utilisateurPartenaireId_unique" UNIQUE ("utilisateurPartenaireId");
```

### 2. APIs Créées

- **GET** `/api/configuration/liaisons-partenaires` - Lister les liaisons
- **POST** `/api/configuration/liaisons-partenaires` - Créer une liaison
- **DELETE** `/api/configuration/liaisons-partenaires/[id]` - Supprimer une liaison

### 3. Interface Utilisateur

- **Page** `/configuration/liaisons-partenaires` - Interface de gestion
- **Composant** `LiaisonDialog` - Dialog de création de liaison
- **Lien** ajouté dans le sidebar Configuration

### 4. APIs Mises à Jour

Toutes les APIs de filtrage partenaire ont été mises à jour pour utiliser le nouveau système :
- `/api/allocations`
- `/api/garanties`
- `/api/mainlevees`
- `/api/mises-en-jeu`
- `/api/garanties/[id]`

## Étapes de Déploiement

### Étape 1: Appliquer la Migration

```bash
# Appliquer les changements de schéma
node scripts/apply-migration.js

# Ou manuellement :
npx prisma db push
npx prisma generate
```

### Étape 2: Créer un Utilisateur Partenaire de Test

```bash
# Créer un utilisateur partenaire avec liaison
node scripts/run-create-partner.js
```

### Étape 3: Vérifier le Déploiement

1. **Connectez-vous en tant qu'administrateur**
2. **Accédez à** `/configuration/liaisons-partenaires`
3. **Vérifiez** que l'interface se charge correctement
4. **Testez** la création/suppression de liaisons

### Étape 4: Migrer les Liaisons Existantes (si nécessaire)

Si vous avez des utilisateurs partenaires existants utilisant l'ancien système basé sur l'email :

```sql
-- Script de migration des liaisons existantes
UPDATE "Partenaire" 
SET "utilisateurPartenaireId" = (
  SELECT u.id 
  FROM "Utilisateur" u 
  WHERE u.email = ("Partenaire".contact->>'email')
  AND u.role = 'Partenaire'
  LIMIT 1
)
WHERE "Partenaire".contact->>'email' IS NOT NULL
AND EXISTS (
  SELECT 1 FROM "Utilisateur" u 
  WHERE u.email = ("Partenaire".contact->>'email')
  AND u.role = 'Partenaire'
);
```

## Fonctionnalités

### Interface de Gestion

- **Tableau** listant tous les utilisateurs partenaires
- **Statut** de liaison (Lié/Non lié)
- **Actions** Lier/Délier
- **Statistiques** en temps réel
- **Validation** des contraintes d'unicité

### Sécurité

- **Permissions** : Seuls Administrateur et GestionnaireGesGar
- **Validation** : Un utilisateur = un partenaire maximum
- **Contraintes** : Unicité au niveau base de données
- **Logs** : Traçabilité des modifications

### Filtrage Automatique

Les utilisateurs partenaires voient maintenant uniquement :
- Leurs allocations
- Leurs garanties
- Les mainlevées de leurs garanties
- Les mises en jeu de leurs garanties

## Test de l'Interface

### Utilisateur de Test Créé

- **Email** : `<EMAIL>`
- **Mot de passe** : `Partner123!`
- **Rôle** : `Partenaire`

### Scénarios de Test

1. **Connexion partenaire** → Vérifier sidebar "Mes Garanties"
2. **Accès APIs** → Vérifier filtrage des données
3. **Interface admin** → Tester création/suppression liaisons
4. **Contraintes** → Tenter de lier un utilisateur déjà lié

## Rollback (si nécessaire)

En cas de problème, vous pouvez revenir à l'ancien système :

```sql
-- Supprimer le nouveau champ
ALTER TABLE "Partenaire" DROP CONSTRAINT "Partenaire_utilisateurPartenaireId_unique";
ALTER TABLE "Partenaire" DROP CONSTRAINT "Partenaire_utilisateurPartenaireId_fkey";
ALTER TABLE "Partenaire" DROP COLUMN "utilisateurPartenaireId";
```

Puis restaurer l'ancien code de filtrage basé sur l'email.

## Support

- **Logs** : Vérifiez les logs serveur pour les erreurs de liaison
- **Base de données** : Utilisez l'interface pour diagnostiquer les liaisons
- **APIs** : Testez les endpoints individuellement

## Avantages du Nouveau Système

1. **Performance** : Liaison directe sans recherche par email
2. **Fiabilité** : Contraintes de base de données
3. **Maintenabilité** : Interface graphique de gestion
4. **Sécurité** : Validation stricte des liaisons
5. **Évolutivité** : Base solide pour futures fonctionnalités

## Prochaines Étapes

1. **Monitoring** : Surveiller les performances après déploiement
2. **Formation** : Former les administrateurs à la nouvelle interface
3. **Documentation** : Mettre à jour la documentation utilisateur
4. **Optimisation** : Analyser les requêtes et optimiser si nécessaire
