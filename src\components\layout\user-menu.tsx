"use client";

import React from "react";
import { signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { 
  User, 
  Settings, 
  LogOut, 
  ChevronDown 
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { UserMenuProps } from "@/types/navbar";

export function UserMenu({ user }: UserMenuProps) {
  const router = useRouter();

  // Get user initials for avatar fallback
  const getUserInitials = (name?: string | null) => {
    if (!name) return "U";
    return name
      .split(" ")
      .map(part => part.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  // Get display name
  const getDisplayName = () => {
    if (user.name) return user.name;
    if (user.email) return user.email.split("@")[0];
    return "Utilisateur";
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      await signOut({ 
        callbackUrl: "/auth/connexion",
        redirect: true 
      });
    } catch (error) {
      console.error("Erreur lors de la déconnexion:", error);
    }
  };

  // Handle profile navigation
  const handleViewProfile = () => {
    router.push("/profile");
  };

  // Handle password change navigation
  const handleChangePassword = () => {
    router.push("/profile/change-password");
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          className="flex items-center gap-2 h-auto p-2 hover:bg-accent/50 transition-colors"
        >
          <div className="flex items-center gap-3">
            <Avatar className="h-8 w-8">
              <AvatarImage 
                src={user.image || undefined} 
                alt={getDisplayName()}
              />
              <AvatarFallback className="bg-primary/10 text-primary text-sm font-medium">
                {getUserInitials(user.name)}
              </AvatarFallback>
            </Avatar>
            
            {/* User info - hidden on small screens */}
            <div className="hidden sm:flex flex-col items-start">
              <span className="text-sm font-medium text-foreground">
                Bonjour, {getDisplayName()}
              </span>
              {user.role && (
                <span className="text-xs text-muted-foreground">
                  {user.role}
                </span>
              )}
            </div>
            
            <ChevronDown className="h-4 w-4 text-muted-foreground" />
          </div>
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent 
        align="end" 
        className="w-56"
        sideOffset={8}
      >
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">
              {getDisplayName()}
            </p>
            {user.email && (
              <p className="text-xs leading-none text-muted-foreground">
                {user.email}
              </p>
            )}
            {user.role && (
              <p className="text-xs leading-none text-muted-foreground">
                Rôle: {user.role}
              </p>
            )}
          </div>
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem 
          onClick={handleViewProfile}
          className="cursor-pointer"
        >
          <User className="mr-2 h-4 w-4" />
          <span>Afficher mon profil</span>
        </DropdownMenuItem>
        
        <DropdownMenuItem 
          onClick={handleChangePassword}
          className="cursor-pointer"
        >
          <Settings className="mr-2 h-4 w-4" />
          <span>Modifier mon mot de passe</span>
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem 
          onClick={handleLogout}
          className="cursor-pointer text-destructive focus:text-destructive"
        >
          <LogOut className="mr-2 h-4 w-4" />
          <span>Déconnexion</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
